<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防重复提交功能演示</title>
    <link rel="stylesheet" href="css/fault.css">
    <style>
        body {
            padding: 30px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-form {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn-submit {
            background-color: #4c6fff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .btn-submit:hover:not(:disabled) {
            background-color: #3a5ae8;
        }
        .feature-list {
            background-color: #e8f4fd;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .feature-list h3 {
            margin-top: 0;
            color: #2c5aa0;
        }
        .feature-list ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 8px;
        }
        .status-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #4c6fff;
            border-radius: 0 4px 4px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>防重复提交功能演示</h1>
        
        <div class="feature-list">
            <h3>🛡️ 实现的防护功能</h3>
            <ul>
                <li><strong>防重复提交</strong>：点击提交后立即禁用按钮，防止重复点击</li>
                <li><strong>加载状态指示</strong>：显示居中的模态弹框，包含旋转加载动画</li>
                <li><strong>页面交互控制</strong>：提交期间阻止用户与其他页面元素交互</li>
                <li><strong>优化成功提示</strong>：在同一弹框内显示成功信息，避免多个弹窗冲突</li>
                <li><strong>状态恢复</strong>：提交完成后正确恢复所有交互状态</li>
                <li><strong>错误处理</strong>：提交失败时重新启用按钮，允许用户重试</li>
            </ul>
        </div>

        <form class="demo-form" id="demoForm">
            <h3>模拟故障登录表单</h3>
            
            <div class="form-group">
                <label for="demo-issue">问题点</label>
                <input type="text" id="demo-issue" name="issue" placeholder="请输入问题描述" required>
            </div>
            
            <div class="form-group">
                <label for="demo-phenomenon">发生现象</label>
                <textarea id="demo-phenomenon" name="phenomenon" rows="3" placeholder="请描述故障现象" required></textarea>
            </div>
            
            <div class="form-group">
                <label for="demo-measures">改善措施</label>
                <textarea id="demo-measures" name="measures" rows="3" placeholder="请输入改善措施"></textarea>
            </div>
            
            <div class="form-group">
                <label for="demo-responsible">改善担当</label>
                <input type="text" id="demo-responsible" name="responsible" placeholder="请输入负责人" required>
            </div>
            
            <button type="submit" class="btn-submit">提交故障信息</button>
            
            <div class="status-info">
                <strong>测试说明：</strong>
                <br>1. 填写表单后点击"提交故障信息"按钮
                <br>2. 观察按钮立即被禁用，防止重复点击
                <br>3. 加载弹框会覆盖整个屏幕，显示提交进度
                <br>4. 模拟网络延迟后显示成功提示
                <br>5. 点击"确认"按钮完成整个流程
            </div>
        </form>
    </div>

    <script>
        // 模拟故障管理器的提交保护功能
        class DemoSubmitProtection {
            constructor() {
                this.successCallback = null;
                this.initForm();
            }
            
            initForm() {
                const form = document.getElementById('demoForm');
                form.onsubmit = async (e) => {
                    e.preventDefault();
                    
                    const submitBtn = form.querySelector('.btn-submit');
                    
                    // 防重复提交检查
                    if (submitBtn.disabled) {
                        console.log('提交已在进行中，忽略重复点击');
                        return;
                    }
                    
                    try {
                        // 显示加载状态并禁用按钮
                        this.showLoadingModal('提交中...');
                        submitBtn.disabled = true;
                        
                        // 模拟网络请求延迟
                        await this.simulateSubmit();
                        
                        // 显示成功提示
                        this.showSuccessModal('故障信息已成功添加', () => {
                            // 成功回调：重置表单
                            form.reset();
                            submitBtn.disabled = false;
                            console.log('表单已重置，按钮已重新启用');
                        });
                        
                    } catch (error) {
                        // 错误处理
                        this.hideLoadingModal();
                        alert('提交失败：' + error.message);
                        submitBtn.disabled = false;
                    }
                };
            }
            
            async simulateSubmit() {
                // 模拟网络请求延迟 2-4 秒
                const delay = 2000 + Math.random() * 2000;
                await new Promise(resolve => setTimeout(resolve, delay));
                
                // 模拟 10% 的失败率
                if (Math.random() < 0.1) {
                    throw new Error('网络连接超时');
                }
            }
            
            showLoadingModal(message = '提交中...') {
                const loadingModal = document.createElement('div');
                loadingModal.id = 'loadingModal';
                loadingModal.className = 'loading-modal';
                loadingModal.innerHTML = `
                    <div class="loading-modal-content">
                        <div class="loading-spinner"></div>
                        <div class="loading-message">${message}</div>
                    </div>
                `;
                
                document.body.appendChild(loadingModal);
                document.body.style.overflow = 'hidden';
                
                setTimeout(() => {
                    loadingModal.classList.add('show');
                }, 10);
            }
            
            hideLoadingModal() {
                const loadingModal = document.getElementById('loadingModal');
                if (loadingModal) {
                    loadingModal.classList.remove('show');
                    setTimeout(() => {
                        if (document.body.contains(loadingModal)) {
                            document.body.removeChild(loadingModal);
                        }
                        document.body.style.overflow = 'auto';
                    }, 300);
                }
            }
            
            showSuccessModal(message, onConfirm) {
                const loadingModal = document.getElementById('loadingModal');
                if (loadingModal) {
                    const content = loadingModal.querySelector('.loading-modal-content');
                    content.innerHTML = `
                        <div class="success-icon">✓</div>
                        <div class="success-message">${message}</div>
                        <button class="success-confirm-btn">确认</button>
                    `;
                    
                    this.successCallback = onConfirm;
                    
                    const confirmBtn = content.querySelector('.success-confirm-btn');
                    confirmBtn.onclick = () => {
                        this.handleSuccessConfirm();
                    };
                }
            }
            
            handleSuccessConfirm() {
                this.hideLoadingModal();
                if (this.successCallback) {
                    this.successCallback();
                    this.successCallback = null;
                }
            }
        }

        // 初始化演示
        document.addEventListener('DOMContentLoaded', () => {
            new DemoSubmitProtection();
        });
    </script>
</body>
</html>
