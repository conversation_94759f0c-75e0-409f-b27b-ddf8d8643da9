<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加载弹框测试</title>
    <link rel="stylesheet" href="css/fault.css">
    <style>
        body {
            padding: 50px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .test-btn {
            background-color: #4c6fff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        .test-btn:hover {
            background-color: #3a5ae8;
        }
        .test-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>加载弹框功能测试</h1>
        <p>点击下面的按钮测试不同的弹框效果：</p>
        
        <button class="test-btn" onclick="testLoadingModal()">测试加载弹框</button>
        <button class="test-btn" onclick="testSuccessModal()">测试成功弹框</button>
        <button class="test-btn" onclick="testSubmitFlow()">测试完整提交流程</button>
        
        <div style="margin-top: 30px;">
            <h3>测试说明：</h3>
            <ul>
                <li><strong>测试加载弹框</strong>：显示带旋转动画的加载提示</li>
                <li><strong>测试成功弹框</strong>：显示成功提示和确认按钮</li>
                <li><strong>测试完整提交流程</strong>：模拟完整的提交过程</li>
            </ul>
        </div>
    </div>

    <script>
        // 简化版的弹框管理器
        class TestModalManager {
            showLoadingModal(message = '提交中...') {
                const loadingModal = document.createElement('div');
                loadingModal.id = 'loadingModal';
                loadingModal.className = 'loading-modal';
                loadingModal.innerHTML = `
                    <div class="loading-modal-content">
                        <div class="loading-spinner"></div>
                        <div class="loading-message">${message}</div>
                    </div>
                `;
                
                document.body.appendChild(loadingModal);
                document.body.style.overflow = 'hidden';
                
                setTimeout(() => {
                    loadingModal.classList.add('show');
                }, 10);
            }
            
            hideLoadingModal() {
                const loadingModal = document.getElementById('loadingModal');
                if (loadingModal) {
                    loadingModal.classList.remove('show');
                    setTimeout(() => {
                        document.body.removeChild(loadingModal);
                        document.body.style.overflow = 'auto';
                    }, 300);
                }
            }
            
            showSuccessModal(message, onConfirm) {
                const loadingModal = document.getElementById('loadingModal');
                if (loadingModal) {
                    const content = loadingModal.querySelector('.loading-modal-content');
                    content.innerHTML = `
                        <div class="success-icon">✓</div>
                        <div class="success-message">${message}</div>
                        <button class="success-confirm-btn">确认</button>
                    `;
                    
                    const confirmBtn = content.querySelector('.success-confirm-btn');
                    confirmBtn.onclick = () => {
                        this.hideLoadingModal();
                        if (onConfirm) onConfirm();
                    };
                }
            }
        }

        const modalManager = new TestModalManager();

        function testLoadingModal() {
            modalManager.showLoadingModal('正在加载...');
            
            // 3秒后自动关闭
            setTimeout(() => {
                modalManager.hideLoadingModal();
            }, 3000);
        }

        function testSuccessModal() {
            modalManager.showLoadingModal('提交中...');
            
            // 2秒后显示成功提示
            setTimeout(() => {
                modalManager.showSuccessModal('操作成功完成！', () => {
                    alert('确认按钮被点击了！');
                });
            }, 2000);
        }

        function testSubmitFlow() {
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = '提交中...';
            
            modalManager.showLoadingModal('正在提交数据...');
            
            // 模拟网络请求延迟
            setTimeout(() => {
                modalManager.showSuccessModal('数据提交成功！', () => {
                    btn.disabled = false;
                    btn.textContent = '测试完整提交流程';
                    alert('提交流程完成，按钮已重新启用！');
                });
            }, 2500);
        }
    </script>
</body>
</html>
